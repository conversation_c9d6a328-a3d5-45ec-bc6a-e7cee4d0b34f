/* TrendSelection 组件自定义样式 */

.trend-topic-card {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.trend-topic-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.trend-topic-card.selected {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border-color: #3b82f6;
}

.trend-detail-panel {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border: 2px solid #60a5fa;
  position: relative;
  overflow: hidden;
}

.trend-detail-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8, #3b82f6);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.trend-content-text {
  line-height: 1.7;
  color: #374151;
}

.trend-content-text::before {
  content: '•';
  color: #6b7280;
  margin-right: 8px;
}

.category-dropdown {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

/* 自定义滚动条样式 - 参考项目规范 */
.custom-scrollbar::-webkit-scrollbar {
  width: 10px;
  height: 10px;
  background-color: #f5f5f519;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: var(--primaryColor);
  transition: 0.3s;
  cursor: pointer;
  border-radius: 6px;
  border: 3px solid transparent;
  background-clip: padding-box;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: var(--primaryColor);
}

.custom-scrollbar::-webkit-scrollbar-button {
  display: none;
}

/* Firefox 滚动条样式 */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: var(--primaryColor) #f5f5f519;
}

.checkbox-custom {
  appearance: none;
  background-color: #fff;
  border: 2px solid #d1d5db;
  border-radius: 0.25rem;
  width: 1.25rem;
  height: 1.25rem;
  position: relative;
  cursor: pointer;
  transition: all 0.2s;
}

.checkbox-custom:checked {
  background-color: #3b82f6;
  border-color: #3b82f6;
}

.checkbox-custom:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 0.875rem;
  font-weight: bold;
}

.checkbox-custom:hover {
  border-color: #9ca3af;
}

.checkbox-custom:checked:hover {
  background-color: #2563eb;
  border-color: #2563eb;
}

/* Dark mode styles */
@media (prefers-color-scheme: dark) {
  .trend-topic-card.selected {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(37, 99, 235, 0.3) 100%);
    border-color: #60a5fa;
  }

  .trend-detail-panel {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(37, 99, 235, 0.3) 100%);
    border-color: #60a5fa;
  }

  .trend-content-text {
    color: #d1d5db;
  }

  .checkbox-custom {
    background-color: #374151;
    border-color: #4b5563;
  }

  .checkbox-custom:hover {
    border-color: #6b7280;
  }
}
