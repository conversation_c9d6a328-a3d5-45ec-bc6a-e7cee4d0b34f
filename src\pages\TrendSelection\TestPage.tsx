import { TrendSelectionPage } from './components/TrendSelectionPage'

/**
 * 趋势选择组件展示页面
 * 访问路径: /p/test/trend-selection
 */
export const TrendSelectionTestPage: React.FC = () => {
  const handleTopicSelect = (topicId: string) => {
    console.log('Topic selected:', topicId)
  }

  const handleReferenceToggle = (isReference: boolean) => {
    console.log('Reference mode:', isReference)
  }

  const handleCategoryChange = (category: string) => {
    console.log('Category changed:', category)
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-7xl mx-auto">
        <TrendSelectionPage
          onTopicSelect={handleTopicSelect}
          onReferenceToggle={handleReferenceToggle}
          onCategoryChange={handleCategoryChange}
        />
      </div>
    </div>
  )
}

export default TrendSelectionTestPage
