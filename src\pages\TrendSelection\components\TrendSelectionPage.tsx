import { cn } from '@/utils'
import { motion } from 'framer-motion'
import { useState } from 'react'
import './TrendSelectionPage.module.css'

interface TrendTopic {
  id: string
  title: string
  subtitle: string
  isSelected?: boolean
}

interface TrendDetail {
  id: string
  content: string[]
  isReference: boolean
}

interface TrendSelectionPageProps {
  className?: string
  onTopicSelect?: (topicId: string) => void
  onReferenceToggle?: (isReference: boolean) => void
}

const mockTrendTopics: TrendTopic[] = [
  {
    id: 'topic-1',
    title: 'Topic 1: Jurassic World Rebirth',
    subtitle: '1st Treading in United States',
  },
  {
    id: 'topic-2',
    title: 'Topic 1: Jurassic World Rebirth',
    subtitle: '1st Treading in United States',
  },
  {
    id: 'topic-3',
    title: 'Topic 1: Jurassic World Rebirth',
    subtitle: '1st Treading in United States',
  },
  {
    id: 'topic-4',
    title: 'Topic 1: Jurassic World Rebirth',
    subtitle: '1st Treading in United States',
  },
  {
    id: 'topic-5',
    title: 'Topic 1: Jurassic World Rebirth',
    subtitle: '1st Treading in United States',
  },
]

const mockTrendDetail: TrendDetail = {
  id: 'trend-1',
  content: [
    'The world of social media is incredibly dynamic, with trends shifting rapidly.',
    'Currently, short-form videos are taking center stage on platforms like TikTok and Instagram Reels. Creators are using these formats to showcase a variety of content, from dance challenges to innovative cooking hacks.',
    'Memes remain a popular form of expression, often capturing the essence of current events or pop culture with a humorous twist.',
    'Sustainability has emerged as a significant topic, with influencers actively promoting eco-friendly products and sustainable lifestyles.',
    'Mental health awareness is on the rise, as many individuals share their personal experiences and offer valuable self-care tips.',
    'Engaging with followers through interactive features like polls and Q&A sessions fosters a sense of community and keeps conversations lively.',
    'As we navigate this ever-changing landscape, it\'s essential to stay informed and adapt to new trends!',
  ],
  isReference: false,
}

export const TrendSelectionPage: React.FC<TrendSelectionPageProps> = ({
  className,
  onTopicSelect,
  onReferenceToggle,
}) => {
  const [trendDetail, setTrendDetail] = useState<TrendDetail>(mockTrendDetail)
  const [hoveredTopic, setHoveredTopic] = useState<string>('')

  const handleTopicSelect = (topicId: string) => {
    onTopicSelect?.(topicId)

    /** 选择话题时，自动滚动到右侧详情区域（移动端） */
    if (window.innerWidth < 1024) {
      setTimeout(() => {
        const detailElement = document.getElementById('trend-detail-section')
        detailElement?.scrollIntoView({ behavior: 'smooth', block: 'start' })
      }, 100)
    }
  }

  const handleReferenceToggle = (checked: boolean) => {
    setTrendDetail(prev => ({ ...prev, isReference: checked }))
    onReferenceToggle?.(checked)
  }

  return (
    <div className={ cn('w-full max-w-7xl mx-auto p-6 space-y-8', className) }>
      {/* Header */}
      <div className="space-y-2">
        <h1 className="text-2xl text-gray-900 font-semibold dark:text-gray-100">
          Choose either one trending topic or a reference post
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          We will create your posts based on your choice
        </p>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
        {/* Left Side - Trending Topics */}
        <div className="space-y-4">

          <div className="space-y-3">
            {mockTrendTopics.map((topic, index) => (
              <motion.div
                key={ topic.id }
                initial={ { opacity: 0, y: 20 } }
                animate={ { opacity: 1, y: 0 } }
                transition={ { duration: 0.3, delay: index * 0.1 } }
                className="relative cursor-pointer overflow-hidden rounded-2xl transition-all duration-300"
                style={ {
                  border: '1.5px solid transparent',
                  backgroundClip: 'padding-box, border-box',
                  backgroundOrigin: 'padding-box, border-box',
                  backgroundImage: 'linear-gradient(to right, #fff, #fff), linear-gradient(90deg, #DD9DFF 0%, #36D3FF 100%)',
                } }
                onClick={ () => handleTopicSelect(topic.id) }
              >
                <div
                  className="relative p-4 transition-all duration-300"
                  style={ {
                    background: hoveredTopic === topic.id
                      ? 'linear-gradient(90deg, rgba(221, 157, 255, 0.10) 0%, rgba(54, 211, 255, 0.10) 100%)'
                      : 'transparent',
                  } }
                  onMouseEnter={ () => setHoveredTopic(topic.id) }
                  onMouseLeave={ () => setHoveredTopic('') }
                >
                  <h3 className="mb-1 text-gray-900 font-medium dark:text-gray-100">
                    {topic.title}
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {topic.subtitle}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Right Side - Trend Detail */}
        <div id="trend-detail-section" className="space-y-4">

          <motion.div
            initial={ { opacity: 0, x: 20 } }
            animate={ { opacity: 1, x: 0 } }
            transition={ { duration: 0.4 } }
            className="relative overflow-hidden rounded-2xl transition-all duration-300"
            style={ {
              border: '1.5px solid transparent',
              backgroundClip: 'padding-box, border-box',
              backgroundOrigin: 'padding-box, border-box',
              backgroundImage: 'linear-gradient(to right, #fff, #fff), linear-gradient(90deg, #DD9DFF 0%, #36D3FF 100%)',
            } }
          >
            <div
              className="p-6 transition-all duration-300 space-y-4"
              style={ {
                background: trendDetail.isReference
                  ? 'linear-gradient(90deg, rgba(221, 157, 255, 0.10) 0%, rgba(54, 211, 255, 0.10) 100%)'
                  : 'transparent',
              } }
            >
              <div className="space-y-3">
                {trendDetail.content.map((paragraph, index) => (
                  <p key={ index } className="text-gray-700 leading-relaxed dark:text-gray-300">
                    -
                    {' '}
                    {paragraph}
                  </p>
                ))}
              </div>

              {/* 复选框在面板内部 */}
              <div className="w-full flex items-center justify-end gap-3 dark:border-gray-600">
                <input
                  type="checkbox"
                  id="reference-checkbox"
                  checked={ trendDetail.isReference }
                  onChange={ e => handleReferenceToggle(e.target.checked) }
                  className="h-5 w-5 border-gray-300 rounded bg-gray-100 text-blue-600 dark:border-gray-600 dark:bg-gray-700 focus:ring-2 focus:ring-blue-500 dark:ring-offset-gray-800 dark:focus:ring-blue-600"
                />
                <label
                  htmlFor="reference-checkbox"
                  className="cursor-pointer text-gray-900 font-medium dark:text-gray-100"
                >
                  Select as my reference
                </label>
              </div>
            </div>
          </motion.div>
        </div>
      </div>

    </div>
  )
}

export default TrendSelectionPage
