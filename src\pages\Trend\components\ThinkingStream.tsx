import type { StateStoreType } from '../stores'
import { AnimateShow } from '@/components/Animate/AnimateShow'
import { Checkmark } from '@/components/Checkbox'
import { LoadingIcon } from '@/components/Loading/LoadingIcon'
import { typeTxt } from '@/utils/tool'
import { motion } from 'framer-motion'
import { ChevronDown, Sparkles } from 'lucide-react'
import { memo, useEffect, useRef, useState } from 'react'

export interface ThinkingStreamProps {
  stateStore: StateStoreType
  className?: string
}

/**
 * Thinking 流式显示组件
 * 完全复制 Distribution 页面的 thinking 消息样式和行为
 */
export const ThinkingStream = memo<ThinkingStreamProps>(({
  stateStore,
  className = '',
}) => {
  const stateSnap = stateStore.use()
  const { isThinkingActive, thinkingContent } = stateSnap
  const contentRef = useRef<HTMLDivElement>(null)

  /** 折叠/展开状态 */
  const [thinkingExpanded, setThinkingExpanded] = useState(false)

  /** 打字机效果状态 */
  const [displayContent, setDisplayContent] = useState('')
  const stopTypingRef = useRef<(() => void) | null>(null)
  const lastContentRef = useRef('')
  const lastIndexRef = useRef(0)

  /** 判断是否完成 */
  const isThinkingDone = thinkingContent && thinkingContent !== '正在思考...' && !isThinkingActive

  /** 自动滚动到底部 */
  useEffect(() => {
    if (contentRef.current && displayContent) {
      contentRef.current.scrollTop = contentRef.current.scrollHeight
    }
  }, [displayContent])

  /** 处理打字机效果 */
  useEffect(() => {
    if (!thinkingContent || thinkingContent === '正在思考...') {
      setDisplayContent('')
      return
    }

    /** 如果内容变化，处理打字效果 */
    if (thinkingContent !== lastContentRef.current) {
      /** 如果有正在进行的打字效果，先停止 */
      if (stopTypingRef.current) {
        stopTypingRef.current()
        stopTypingRef.current = null
      }

      /** 确定继续的位置 */
      const continueFromIndex = thinkingContent.startsWith(lastContentRef.current)
        ? lastIndexRef.current
        : 0

      /** 启动打字效果 */
      const { stop } = typeTxt({
        content: thinkingContent,
        continueFromIndex,
        speed: import.meta.env.DEV
          ? 8
          : 20,
        callback: (text) => {
          setDisplayContent(text)
          lastIndexRef.current = text.length
        },
      })

      /** 保存停止函数和当前内容 */
      stopTypingRef.current = stop
      lastContentRef.current = thinkingContent
    }
  }, [thinkingContent])

  /** 清理打字效果 */
  useEffect(() => {
    return () => {
      if (stopTypingRef.current) {
        stopTypingRef.current()
      }
    }
  }, [])

  if (!isThinkingActive) {
    return null
  }

  return (
    <motion.div
      initial={ { opacity: 0, y: 20, height: 0 } }
      animate={ { opacity: 1, y: 0, height: 'auto' } }
      exit={ { opacity: 0, y: -20, height: 0 } }
      transition={ { duration: 0.3 } }
      className={ `thinking-stream-container ${className}` }
    >
      <div className="mb-2 flex flex-col space-y-2">
        {/* 头部 - 完全复制 Distribution 页面的样式 */}
        <div
          className="w-fit flex cursor-pointer items-center rounded-2xl bg-slate-100 px-3 py-1.5 text-slate-500 space-x-2 dark:bg-slate-600 dark:text-slate-300"
          onClick={ () => setThinkingExpanded(!thinkingExpanded) }
        >
          <Sparkles size={ 14 } className="text-slate-400 dark:text-slate-400" />
          <span className="text-xs font-medium">
            {isThinkingDone
              ? 'Thinking done'
              : 'Thinking...'}
          </span>

          {isThinkingDone
            ? (
                <Checkmark size={ 14 } />
              )
            : (
                <LoadingIcon size={ 14 } />
              )}

          <motion.div
            animate={ {
              rotate: thinkingExpanded
                ? 180
                : 0,
            } }
            transition={ { duration: 0.2 } }
            className="ml-auto"
          >
            <ChevronDown className="h-3 w-3 text-slate-400" />
          </motion.div>
        </div>

        {/* 内容区域 - 完全复制 Distribution 页面的样式 */}
        <AnimateShow
          show={ thinkingExpanded }
          variants={ {
            initial: { opacity: 1, height: 'auto' },
            animate: { opacity: 1, height: 'auto' },
            exit: { opacity: 0, height: 0 },
          } }
        >
          <div
            ref={ contentRef }
            className="max-h-40 overflow-y-auto whitespace-pre-wrap border-l border-slate-200 pl-2 text-xs text-slate-500 dark:border-slate-700 dark:text-slate-400"
          >
            {displayContent || thinkingContent || '正在思考...'}
            {/* 光标效果 - 只在正在打字或等待时显示 */}
            {(!isThinkingDone && (displayContent.length < thinkingContent.length || !thinkingContent || thinkingContent === '正在思考...')) && (
              <span className="ml-1 inline-block h-3 w-1 animate-pulse bg-slate-500" />
            )}
          </div>
        </AnimateShow>
      </div>
    </motion.div>
  )
})

ThinkingStream.displayName = 'ThinkingStream'
