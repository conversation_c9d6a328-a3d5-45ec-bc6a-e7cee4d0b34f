/**
 * 趋势选择页面相关类型定义
 */

export interface TrendTopic {
  id: string
  title: string
  subtitle: string
  isSelected?: boolean
  category?: string
  popularity?: number
  region?: string
}

export interface TrendDetail {
  id: string
  title: string
  content: string[]
  isReference: boolean
  author?: string
  publishDate?: string
  engagement?: {
    likes: number
    shares: number
    comments: number
  }
}

export interface PostCategory {
  id: string
  name: string
  description?: string
  icon?: string
}

export interface RednoteReference {
  id: string
  title: string
  content: string
  category: string
  author: string
  publishDate: string
  thumbnailUrl?: string
  engagement: {
    likes: number
    shares: number
    comments: number
  }
}

export interface TrendSelectionState {
  selectedTopic: string | null
  selectedTrendDetail: TrendDetail | null
  selectedCategory: string
  isReferenceMode: boolean
  selectedReferences: string[]
}

export interface TrendSelectionActions {
  onTopicSelect: (topicId: string) => void
  onTrendDetailSelect: (detail: TrendDetail) => void
  onReferenceToggle: (isReference: boolean) => void
  onCategoryChange: (category: string) => void
  onReferenceSelect: (referenceId: string) => void
  onSubmit: (data: TrendSelectionState) => void
}

export type TrendSelectionPageProps = {
  className?: string
  initialState?: Partial<TrendSelectionState>
} & Partial<TrendSelectionActions>
