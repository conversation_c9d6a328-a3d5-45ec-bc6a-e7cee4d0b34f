import type { MarketStep1Params } from '@/api/MarketApi'
import type { TrendStep1Params } from '@/api/TrendApi'
import type { MdToCodePreviewType, MessageStoreType, ReportStoreType, StateStoreType, StepStateType, TaskStoreType } from '../stores'

import type { ParsedStreamData } from '../stores/cozeStreamApi'
import { useBindWinEvent } from '@/hooks'
import { cn } from '@/utils'
import { motion } from 'framer-motion'

import { memo, useCallback, useEffect, useInsertionEffect, useRef, useState } from 'react'
import ChatWorkflow from '../../ChatV2/components/ChatWorkflow'
import TopBar from '../../ChatV2/components/TopBar'
import { DistributionEvent, eventBus } from '../constants'
import { trendAg } from '../stores'
import { callCozeStreamAPI } from '../stores/cozeStreamApi'
import { addReportItem, createCardMessage, removeMessage } from '../stores/create'
import { ChatHistory } from './ChatHistory'
import { ReportPreview } from './ReportComponents/ReportPreview'
import { ThinkingStream } from './ThinkingStream'

export const ChatPage = memo<ChatPageProps>((
  {
    style,
    className,
    taskStore,
    stateStore,
    messageStore,
    mdToCodePreview,
    resetDistributionStore: _resetDistributionStore,
    reportStore,
    stepState,

    // TopBar 相关 props
    showTopBar = false,
    topBarAgents = [],
    onTopBarAgentClick,
    topBarDropdownExpanded = false,
    onTopBarDropdownToggle,

    // ChatV2 流程相关 props
    onStartAIAnalysis,
  },
) => {
  const { formData, isReportOpen, chatV2FlowMode, userDescription, uploadedImage } = stateStore.use()
  taskStore.use() // 保持响应式连接

  const chatHistoryRef = useRef<{ scrollToBottom: () => void }>(null)
  const stateStoreRef = useRef(stateStore)
  const taskStoreRef = useRef(taskStore)

  /** 更新 refs */
  stateStoreRef.current = stateStore
  taskStoreRef.current = taskStore

  // ChatV2 流程状态管理
  const [isThinking, setIsThinking] = useState(false)
  const [showForm, setShowForm] = useState(false)
  const [showWorkflow, setShowWorkflow] = useState(false)
  const [thinkingExpanded, setThinkingExpanded] = useState(true)
  const [isAnalysisStarted, setIsAnalysisStarted] = useState(false) // 控制是否已开始分析
  const [isButtonLoading, setIsButtonLoading] = useState(false) // 控制Continue to Strategy按钮的loading状态
  const [isButtonDisabled, setIsButtonDisabled] = useState(false) // 控制Continue to Strategy按钮的disabled状态
  const [cardsCreated, setCardsCreated] = useState(false) // 防重复创建标志
  const [competitiveReportItemId, setCompetitiveReportItemId] = useState<string>('') // 存储竞争对手分析报告项ID

  /** 表单状态管理 */
  const [formDataLocal, setFormDataLocal] = useState<Partial<MarketStep1Params>>({
    brand: '',
    product_name: '',
    industry: '',
    competitor: '',
    product: '', // 独立的表单字段，不使用 userDescription
    pic: '', // 独立的表单字段，不使用 uploadedImage
    industry_id: 83, // 默认值
    ip: '', // 默认值
    role: '', // 默认值
  })
  const [formErrors, setFormErrors] = useState<Record<string, string>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  /** 处理 ChatV2 流程状态变化 */
  useEffect(() => {
    if (chatV2FlowMode === 'thinking') {
      /** 启动 Thinking 流程 */
      setIsThinking(true)
      setTimeout(() => {
        setIsThinking(false)
        setShowForm(true)
        stateStore.chatV2FlowMode = 'form'
      }, 3000)
    }
    else if (chatV2FlowMode === 'form') {
      setShowForm(true)
    }
    else if (chatV2FlowMode === 'workflow') {
      setShowForm(false)
      setShowWorkflow(true)
    }
  }, [chatV2FlowMode, stateStore])

  /** 监听ButtonStateManager的状态变化，同步按钮状态 */
  useEffect(() => {
    const handleButtonStateChange = (event: CustomEvent) => {
      const { loading, disabled } = event.detail
      setIsButtonLoading(loading)
      setIsButtonDisabled(disabled)
    }

    window.addEventListener('chatWorkflowButtonStateChange', handleButtonStateChange as EventListener)

    return () => {
      window.removeEventListener('chatWorkflowButtonStateChange', handleButtonStateChange as EventListener)
    }
  }, [])

  /** 当分析开始时，自动滚动到底部 */
  useEffect(() => {
    if (isAnalysisStarted) {
      /** 使用 requestAnimationFrame 确保 DOM 已更新 */
      requestAnimationFrame(() => {
        /** 第一次滚动：确保容器可见 */
        const container = document.querySelector('.ChatPageContainer .overflow-auto')
        if (container) {
          container.scrollTo({
            top: container.scrollHeight,
            behavior: 'smooth',
          })
        }

        /** 延迟后再次滚动：确保内容加载完成 */
        setTimeout(() => {
          if (container) {
            container.scrollTo({
              top: container.scrollHeight,
              behavior: 'smooth',
            })
          }
          /** ChatHistory 内部滚动 */
          chatHistoryRef.current?.scrollToBottom?.()
        }, 300)
      })
    }
  }, [isAnalysisStarted])

  /** 表单处理函数 */
  const handleFormChange = (field: keyof MarketStep1Params, value: string) => {
    setFormDataLocal(prev => ({ ...prev, [field]: value }))
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const handleFormSubmit = async () => {
    if (isSubmitting)
      return

    setFormErrors({})
    const errors: Record<string, string> = {}

    /** 验证必填字段 */
    const requiredFields = [
      { key: 'brand', label: 'Please enter the brand name to continue.' },
      { key: 'product_name', label: 'Please enter the product name to continue.' },
      { key: 'industry', label: 'Please enter the industry to continue.' },
      { key: 'competitor', label: 'Please enter the competitor information to continue.' },
    ]

    requiredFields.forEach(({ key, label }) => {
      const value = formDataLocal[key as keyof typeof formDataLocal]
      if (!value || (typeof value === 'string' && !value.trim())) {
        errors[key] = label
      }
    })

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors)
      return
    }

    setIsSubmitting(true)
    try {
      /** 准备完整的表单数据 */
      const completeFormData = {
        industry: formDataLocal.industry || '',
        industry_id: 83,
        product_name: formDataLocal.product_name || '',
        ip: '专研彩妆',
        brand: formDataLocal.brand || '',
        role: '中国时尚彩妆领导品牌',
        product: formDataLocal.product || '',
        pic: formDataLocal.pic || '',
        competitor: formDataLocal.competitor || '',
        company: formDataLocal.brand || '',
        marketing_strategy: '好用',
        product_market: '',
        competitor_info: '女性彩妆',
      }

      /** 保存到 stateStore 并切换到工作流模式 */
      stateStore.cacheFormData = completeFormData
      stateStore.chatV2FlowMode = 'workflow'
    }
    catch (error) {
      console.error('Form submission failed:', error)
    }
    finally {
      setIsSubmitting(false)
    }
  }

  /** 转换 MarketStep1Params 到 TrendStep1Params */
  const convertToTrendParams = (marketParams: MarketStep1Params): TrendStep1Params => {
    return {
      brand: marketParams.brand,
      product_name: marketParams.product_name,
      industry: marketParams.industry,
      industry_id: marketParams.industry_id,
      competitor: marketParams.competitor || '',
      product: marketParams.product,
      role: marketParams.role,
      company: marketParams.company || marketParams.brand,
      pic: marketParams.pic,
      ip: marketParams.ip,
      marketing_strategy: marketParams.marketing_strategy || '',
      product_market: marketParams.product_market || '',
      competitor_info: marketParams.competitor_info || '',
    }
  }

  /** 为第一个卡片调用流式API更新报告内容 */
  const updateFirstCardReport = useCallback(async (reportItemId: string) => {
    console.warn('[updateFirstCardReport] 开始为第一个卡片调用流式API:', reportItemId)

    try {
      /** 标记是否已接收到第一条内容，用于清除占位文本 */
      let isFirstContent = true

      /** 调用 Coze 流式接口 */
      await callCozeStreamAPI(
        /** 处理流式数据 */
        (data: ParsedStreamData) => {
          const { reportStore } = trendAg
          const targetItem = reportStore.items.find(item => item.id === reportItemId)

          if (data.type === 'title' && data.title && targetItem) {
            /** 更新报告标题 */
            targetItem.title = data.title
          }
          else if (data.type === 'text' && data.content && targetItem) {
            /** 更新报告内容 */
            if (isFirstContent) {
              /** 首次接收内容时，清除占位文本 */
              targetItem.content = data.content
              isFirstContent = false
            }
            else {
              /** 后续内容追加 */
              targetItem.content += data.content
            }
          }
          else if (data.type === 'complete') {
            console.warn('[updateFirstCardReport] ✅ 流式数据接收完成')
          }
        },
        /** 错误处理 */
        (error: Error) => {
          console.error('[updateFirstCardReport] 流式API调用失败:', error)
          const { reportStore } = trendAg
          const targetItem = reportStore.items.find(item => item.id === reportItemId)
          if (targetItem) {
            targetItem.content = `数据获取失败：${error.message}\n\n请检查网络连接后重试。`
          }
        },
        /** 完成处理 */
        () => {
          console.warn('[updateFirstCardReport] 流式响应完成')
        },
      )
    }
    catch (error) {
      console.error('[updateFirstCardReport] 执行失败:', error)
    }
  }, [])

  /** 创建自定义的两个策略卡片 */
  const createCustomStrategyCards = useCallback(() => {
    /** 防重复创建检查 */
    if (cardsCreated) {
      console.warn('[createCustomStrategyCards] 卡片已存在，跳过创建')
      return
    }

    console.warn('[createCustomStrategyCards] 开始创建卡片，时间戳:', Date.now())

    const curStep = 'step0' as any
    const currentLeftIcon = 'researchAnalyst' // 使用 Research Analyst 图标

    /** 创建第一个报告项 - Interior Design Industry Research Report 2025 */
    const industryReportItem = addReportItem({
      type: 'markdown',
      title: 'Interior Design Industry Research Report 2025',
      content: 'Analyzing interior design industry trends and market dynamics...',
      meta: {
        step: curStep,
        canTransformCode: true,
      },
    }, true, true, trendAg)

    /** 创建第二个报告项 - Competitive Analysis */
    const competitiveReportItem = addReportItem({
      type: 'markdown',
      title: 'Competitive Analysis',
      content: 'Analyzing competitor strategies and positioning opportunities...',
      meta: {
        step: curStep,
        canTransformCode: true,
      },
    }, true, false, trendAg)

    /** 保存竞争对手分析报告项ID */
    setCompetitiveReportItemId(competitiveReportItem.id)
    console.warn('[createCustomStrategyCards] 设置竞争对手分析报告项ID:', competitiveReportItem.id)

    /** 手动设置基础时间戳，确保卡片显示在正确位置 */
    const baseTimestamp = Date.now() - 10000 // 减去10秒，确保卡片显示在其他消息之前

    /** 第一个卡片：Interior Design Industry Research Report 2025 */
    const firstCard = createCardMessage(
      {
        title: 'Interior Design Industry Research Report 2025',
        description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit...',
        variant: 'success',
        onClick: () => {
          stateStoreRef.current.isReportOpen = true
          taskStoreRef.current.currentStep = curStep
          eventBus.emit(DistributionEvent.SetActiveTab, industryReportItem.id)
        },
        cardConfig: {
          leftIcon: {
            show: true,
            icon: currentLeftIcon,
            size: 'lg' as const,
          },
          rightIcon: {
            show: true,
            icon: 'card-right-icon1',
            size: 'md' as const,
          },
          content: {
            title: 'Interior Design Industry Research Report 2025',
            description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit...',
          },
          layout: 'simple' as const,
        },
      },
      {
        meta: {
          step: curStep,
          cardId: 'interior-design-report',
        },
      },
      trendAg,
    )

    /** 第二个卡片：Competitive Analysis */
    const secondCard = createCardMessage(
      {
        title: 'Competitive Analysis',
        description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit...',
        variant: 'success',
        onClick: () => {
          stateStoreRef.current.isReportOpen = true
          taskStoreRef.current.currentStep = curStep
          eventBus.emit(DistributionEvent.SetActiveTab, competitiveReportItem.id)
        },
        cardConfig: {
          leftIcon: {
            show: true,
            icon: currentLeftIcon,
            size: 'lg' as const,
          },
          rightIcon: {
            show: true,
            icon: 'card-right-icon2',
            size: 'md' as const,
          },
          content: {
            title: 'Competitive Analysis',
            description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit...',
          },
          layout: 'simple' as const,
        },
      },
      {
        meta: {
          step: curStep,
          cardId: 'competitive-analysis',
        },
      },
      trendAg,
    )

    /** 手动调整时间戳，确保卡片按正确顺序显示在其他消息之前 */
    firstCard.timestamp = baseTimestamp + 1 // 第一个卡片
    secondCard.timestamp = baseTimestamp + 2 // 第二个卡片

    console.warn('[createCustomStrategyCards] 卡片已创建，时间戳:', {
      firstCard: { id: firstCard.id, timestamp: firstCard.timestamp, title: firstCard.card?.title },
      secondCard: { id: secondCard.id, timestamp: secondCard.timestamp, title: secondCard.card?.title },
      totalMessages: trendAg.messageStore.messages.length,
      messageTypes: trendAg.messageStore.messages.map(msg => ({ id: msg.id, type: msg.type, timestamp: msg.timestamp })),
    })

    /** 为第一个卡片调用流式API更新报告内容 */
    setTimeout(() => {
      updateFirstCardReport(industryReportItem.id)
    }, 200) // 短暂延迟确保卡片创建完成

    /** 自动打开报告面板并显示第一个卡片的tab */
    setTimeout(() => {
      stateStoreRef.current.isReportOpen = true
      eventBus.emit(DistributionEvent.SetActiveTab, industryReportItem.id)
      console.warn('[createCustomStrategyCards] 自动打开报告面板，显示第一个卡片tab')
    }, 300) // 稍后执行，确保卡片和报告项都已创建

    /** 设置创建完成标志，防止重复创建 */
    setCardsCreated(true)
  }, [cardsCreated, updateFirstCardReport]) // 添加 updateFirstCardReport 依赖

  /** 创建Continue to Strategy按钮点击后的新卡片 */
  const createStrategyButtonCard = useCallback(() => {
    const curStep = 'step0' as any
    const currentLeftIcon = 'researchAnalyst' // 使用 Research Analyst 图标

    /** 创建报告项 - Strategy Implementation Plan */
    const strategyReportItem = addReportItem({
      type: 'markdown',
      title: '_internal_strategy_implementation',
      content: 'Generating comprehensive strategy implementation plan...',
      meta: {
        step: curStep,
        canTransformCode: true,
      },
    }, true, true, trendAg) // 自动打开报告面板

    /** 手动设置时间戳，确保卡片显示在 ThinkingStream 下方 */
    const baseTimestamp = Date.now()

    /** 创建卡片：Strategy Implementation Plan */
    const strategyCard = createCardMessage(
      {
        title: 'Strategy Implementation Plan',
        description: 'Comprehensive plan for implementing your marketing strategy with actionable steps and timelines.',
        variant: 'success',
        onClick: () => {
          stateStoreRef.current.isReportOpen = true
          taskStoreRef.current.currentStep = curStep
          eventBus.emit(DistributionEvent.SetActiveTab, strategyReportItem.id)
        },
        cardConfig: {
          leftIcon: {
            show: true,
            icon: currentLeftIcon,
            size: 'lg' as const,
          },
          rightIcon: {
            show: true,
            icon: 'card-right-icon1',
            size: 'md' as const,
          },
          content: {
            title: 'Strategy Implementation Plan',
            description: 'Comprehensive plan for implementing your marketing strategy with actionable steps and timelines.',
          },
          layout: 'simple' as const,
        },
      },
      {
        meta: {
          step: curStep,
          cardId: 'strategy-implementation-plan',
        },
      },
      trendAg,
    )

    /** 设置时间戳，确保卡片显示在正确位置 */
    strategyCard.timestamp = baseTimestamp

    console.warn('[createStrategyButtonCard] Strategy按钮卡片已创建:', {
      card: { id: strategyCard.id, timestamp: strategyCard.timestamp, title: strategyCard.card?.title },
      reportItem: { id: strategyReportItem.id, title: strategyReportItem.title },
    })
  }, [])

  /** 处理开始分析按钮点击 */
  const handleStartAnalysis = async () => {
    if (isButtonLoading || isButtonDisabled)
      return

    /** 标记分析已开始，显示分析结果区域 */
    setIsAnalysisStarted(true)
    setIsButtonLoading(true)

    /** 创建Strategy按钮点击后的新卡片 */
    createStrategyButtonCard()

    /** 获取当前的竞争对手分析报告项ID */
    const currentCompetitiveReportItemId = competitiveReportItemId
    console.warn('[handleStartAnalysis] 当前竞争对手分析报告项ID:', currentCompetitiveReportItemId)

    /** 如果ID为空，尝试从reportStore中查找 */
    let finalCompetitiveReportItemId = currentCompetitiveReportItemId
    if (!finalCompetitiveReportItemId) {
      const competitiveReport = trendAg.reportStore.items.find(item => item.title === 'Competitive Analysis')
      if (competitiveReport) {
        finalCompetitiveReportItemId = competitiveReport.id
        console.warn('[handleStartAnalysis] 从reportStore中找到竞争对手分析报告项ID:', finalCompetitiveReportItemId)
      }
      else {
        console.error('[handleStartAnalysis] 无法找到竞争对手分析报告项')
      }
    }

    /** 开始分析后持续监听消息变化并滚动 */
    const scrollInterval = setInterval(() => {
      const container = document.querySelector('.ChatPageContainer .overflow-auto')
      if (container) {
        const isNearBottom = container.scrollHeight - container.scrollTop - container.clientHeight < 100
        /** 只有在接近底部时才自动滚动，避免干扰用户手动滚动 */
        if (isNearBottom) {
          container.scrollTo({
            top: container.scrollHeight,
            behavior: 'smooth',
          })
        }
      }
    }, 500)

    try {
      /** 同时调用两个 Coze 流式接口 */
      const { executeResearchAnalystWithCoze, executeCompetitorAnalysisWithCoze } = await import('../stores/chatActions')

      console.warn('[handleStartAnalysis] 准备执行两个并行任务:', {
        finalCompetitiveReportItemId,
        executeResearchAnalystWithCoze: typeof executeResearchAnalystWithCoze,
        executeCompetitorAnalysisWithCoze: typeof executeCompetitorAnalysisWithCoze,
      })

      /** 构建任务数组 */
      const promises = [
        (async () => {
          console.warn('[handleStartAnalysis] 🚀 开始执行 executeResearchAnalystWithCoze')
          const result = await executeResearchAnalystWithCoze()
          console.warn('[handleStartAnalysis] ✅ executeResearchAnalystWithCoze 完成')
          return result
        })(),
      ]

      /** 只有当 finalCompetitiveReportItemId 有效时才添加第二个任务 */
      if (finalCompetitiveReportItemId && finalCompetitiveReportItemId.trim() !== '') {
        promises.push(
          (async () => {
            console.warn('[handleStartAnalysis] 🚀 开始执行 executeCompetitorAnalysisWithCoze，参数:', finalCompetitiveReportItemId)
            const result = await executeCompetitorAnalysisWithCoze(finalCompetitiveReportItemId)
            console.warn('[handleStartAnalysis] ✅ executeCompetitorAnalysisWithCoze 完成')
            return result
          })(),
        )
      }
      else {
        console.error('[handleStartAnalysis] ❌ finalCompetitiveReportItemId 无效，跳过竞争对手分析任务:', finalCompetitiveReportItemId)
      }

      console.warn('[handleStartAnalysis] Promise.all 开始执行，任务数量:', promises.length)
      await Promise.all(promises)
      console.warn('[handleStartAnalysis] Promise.all 执行完成')

      /** 分析完成后清理定时器 */
      setTimeout(() => clearInterval(scrollInterval), 2000)
      setIsButtonDisabled(true) // 完成后禁用按钮
    }
    catch (error) {
      console.error('Start analysis failed:', error)
      /** 如果失败，重置状态 */
      setIsAnalysisStarted(false)
      clearInterval(scrollInterval)
    }
    finally {
      setIsButtonLoading(false)
    }
  }

  useBindWinEvent('resize', () => {
    stateStore.isReportOpen = false
    stateStore.isAgentCollapsed = true
  })

  /** 当页面加载且有表单数据时，创建一个开始按钮让用户手动启动 */
  useEffect(() => {
    if (!formData) {
      return
    }

    /** 创建一个初始任务，让用户点击开始 */
    if (taskStore.agentTasks.length === 0) {
      taskStore.agentTasks.push({
        id: 'start',
        title: 'Marketing Team Ready',
        description: '点击下方按钮开始执行营销方案生成',
        status: 'waiting',
        actions: [{ label: 'Continue to Strategy', type: 'primary' }],
        step: 'step0' as any,
      })
    }
  }, [formData, taskStore.agentTasks])

  /** 监听 showWorkflow 变化，在表单提交后立即创建策略卡片 */
  useEffect(() => {
    if (showWorkflow && !cardsCreated) {
      /** 立即创建卡片，通过时间戳控制显示位置 */
      setTimeout(() => {
        /** 调用自定义的策略卡片创建方法 */
        createCustomStrategyCards()
      }, 100) // 短暂延迟确保状态更新完成
    }
    else if (!showWorkflow && cardsCreated) {
      /** 当 showWorkflow 变为 false 时，重置创建标志，允许下次重新创建 */
      console.warn('[useEffect] 重置卡片创建标志')
      setCardsCreated(false)
    }
  }, [showWorkflow, cardsCreated, createCustomStrategyCards])

  useInsertionEffect(() => {
    const overflow = document.body.style.overflow
    document.body.style.overflow = 'hidden'
    return () => {
      document.body.style.overflow = overflow
    }
  }, [])

  return <div
    className={ cn(
      'ChatPageContainer flex flex-row h-full overflow-hidden p-4 gap-4',
      className,
    ) }
    style={ style }
  >
    {/* 主内容区 */}
    <div className="flex flex-1 flex-col overflow-hidden rounded-2xl bg-white">
      {/* TopBar - 只在左侧聊天区域上方显示 */}
      {showTopBar && (
        <div className="flex-shrink-0 border-b border-gray-100">
          <TopBar
            agents={ topBarAgents }
            onAgentClick={ onTopBarAgentClick }
            dropdownExpanded={ topBarDropdownExpanded }
            onDropdownToggle={ onTopBarDropdownToggle }
            containerClassName="relative"
          />
        </div>
      )}

      {/* 聊天内容区域 */}
      <div className="flex-1 overflow-hidden">
        {chatV2FlowMode
          ? (
            // ChatV2 流程界面 - 使用可滚动容器
              <div className="relative h-full flex flex-col">
                <div className="flex-1 overflow-auto pb-28">
                  {' '}
                  {/* 为底部输入框预留空间 */}
                  <div className="mx-auto max-w-4xl">
                    <ChatWorkflow
                      showContentDisplay
                      content={ userDescription }
                      uploadedImage={ uploadedImage }
                      showThinking
                      isThinking={ isThinking }
                      thinkingExpanded={ thinkingExpanded }
                      onThinkingToggle={ setThinkingExpanded }
                      showWorkflow={ showWorkflow }
                      showForm={ showForm && !showWorkflow }
                      formData={ formDataLocal }
                      formErrors={ formErrors }
                      formUploadedImage={ formDataLocal.pic }
                      isSubmitting={ isSubmitting }
                      onFormChange={ handleFormChange }
                      onFormSubmit={ handleFormSubmit }
                      onImageUpload={ (file) => {
                        const reader = new FileReader()
                        reader.onload = (e) => {
                          const result = e.target?.result as string
                          handleFormChange('pic', result)
                        }
                        reader.readAsDataURL(file)
                      } }
                      onImageRemove={ () => handleFormChange('pic', '') }
                      workflowTitle="Your AI Agent Team's Action plan"
                      workflowDescription="Perfect! Your brand profile is ready. Now I'm bringing together my AI agent team to create amazing trend analysis for you. Here's how we'll work together:"
                      onStartAnalysis={ handleStartAnalysis }
                      showAskInput={ false } // 禁用内置的 Ask 输入框
                      askInputPlaceholder=""
                      askInputValue=""
                      askInputDisabled
                      onAskInputSubmit={ () => {} }
                    />

                    {/* 分析结果区域 - 表单提交后立即显示，包含策略卡片和Continue to Strategy按钮 */}
                    {showWorkflow && (
                      <motion.div
                        className="border-gray-200"
                        initial={ { opacity: 0, y: 20 } }
                        animate={ { opacity: 1, y: 0 } }
                        transition={ { duration: 0.5 } }
                      >
                        {/* 策略卡片显示区域 - 在motion组件最顶部 */}
                        <div className="mb-6">
                          <ChatHistory
                            taskStore={ taskStore }
                            messageStore={ messageStore }
                            ref={ chatHistoryRef }
                            className="min-h-0 w-full"
                            onDeleteMessage={ removeMessage }
                            stateStore={ stateStore }
                          />
                        </div>

                        {/* Continue to Strategy 按钮 - 在卡片下方 */}
                        <div className="mb-6 flex justify-start">
                          <button
                            className={ cn(
                              'px-8 py-3 rounded-full text-sm font-medium transition-colors bg-black text-white hover:bg-gray-800',
                              'disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-black',
                              isButtonLoading && 'cursor-wait',
                            ) }
                            disabled={ isButtonLoading || isButtonDisabled }
                            onClick={ handleStartAnalysis }
                          >
                            <div className="flex items-center gap-2">
                              {isButtonLoading && (
                                <div className="h-4 w-4 animate-spin border-2 border-white border-t-transparent rounded-full" />
                              )}
                              Continue to Strategy
                            </div>
                          </button>
                        </div>

                        {/* Thinking 流式显示组件 - 在按钮下方 */}
                        <ThinkingStream stateStore={ stateStore } />
                      </motion.div>
                    )}
                  </div>
                </div>

                {/* 悬浮的输入框 - 始终显示在底部 */}
                {showWorkflow && (
                  <div className="absolute bottom-0 left-0 right-0 bg-white p-4">
                    <div className="mx-auto max-w-4xl">
                      <div className="flex items-center gap-3 border rounded-lg bg-gray-50 p-3">
                        <input
                          type="text"
                          value={ isAnalysisStarted
                            ? 'AI analysis in progress...'
                            : 'Analysis will start from workflow above' }
                          readOnly
                          className="flex-1 border-none bg-transparent text-gray-700 outline-none placeholder-gray-400"
                          placeholder={ isAnalysisStarted
                            ? 'Analysis running...'
                            : 'Click the button in workflow to start analysis...' }
                        />
                        {isAnalysisStarted && (
                          <div className="h-8 w-8 flex items-center justify-center">
                            <div className="h-4 w-4 animate-spin border-b-2 border-blue-500 rounded-full"></div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )
          : (
            /** 正常的聊天界面 */
              <div className="relative h-full flex flex-col">
                <motion.div
                  layout
                  className={ cn(
                    'flex-1 flex flex-col gap-4 max-w-4xl mx-auto overflow-auto',
                    isReportOpen && 'max-w-3xl',
                  ) }>

                  <ChatHistory
                    taskStore={ taskStore }
                    messageStore={ messageStore }
                    ref={ chatHistoryRef }
                    className="min-h-0 w-full flex-1 p-4"
                    onDeleteMessage={ removeMessage }
                    stateStore={ stateStore }
                  />
                </motion.div>

                {/* 预留悬浮输入框位置 - 目前不显示 */}
                {false && (
                  <div className="absolute bottom-0 left-0 right-0 border-t border-gray-200 bg-white p-4">
                    <div className="mx-auto max-w-4xl">
                      <div className="flex items-center gap-3 border rounded-lg bg-gray-50 p-3">
                        <input
                          type="text"
                          placeholder="Type your message..."
                          className="flex-1 border-none bg-transparent text-gray-700 outline-none placeholder-gray-400"
                        />
                        <button className="h-8 w-8 flex items-center justify-center rounded-full bg-blue-500 text-white transition-colors hover:bg-blue-600">
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M5 12H19M19 12L12 5M19 12L12 19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
      </div>
    </div>

    <ReportPreview
      isOpen={ isReportOpen }
      onClose={ () => stateStore.isReportOpen = !stateStore.isReportOpen }
      className="flex-shrink-0"
      reportStore={ reportStore }
      taskStore={ taskStore }
      stepState={ stepState }
      mdToCodePreview={ mdToCodePreview }
      stateStore={ stateStore }
    />
  </div>
})

ChatPage.displayName = 'ChatPage'

export type ChatPageProps = {
  className?: string
  style?: React.CSSProperties
  children?: React.ReactNode

  taskStore: TaskStoreType
  stateStore: StateStoreType
  messageStore: MessageStoreType
  mdToCodePreview: MdToCodePreviewType
  resetDistributionStore: () => void
  reportStore: ReportStoreType
  stepState: StepStateType

  // TopBar 相关 props
  showTopBar?: boolean
  topBarAgents?: any[]
  onTopBarAgentClick?: (agent: any) => void
  topBarDropdownExpanded?: boolean
  onTopBarDropdownToggle?: (expanded: boolean) => void

  // ChatV2 流程相关 props
  onStartAIAnalysis?: (formData: any) => Promise<void>
}
& React.DetailedHTMLProps<React.ImgHTMLAttributes<HTMLDivElement>, HTMLDivElement>
