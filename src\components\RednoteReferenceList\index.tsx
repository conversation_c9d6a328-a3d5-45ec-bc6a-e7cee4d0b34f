import { But<PERSON>, Select } from 'antd'
import classNames from 'clsx'
import { Eye, Heart, MessageCircle, Share } from 'lucide-react'
import { useState } from 'react'

/** 模拟数据 */
const mockData = {
  mainPost: {
    id: '1',
    user: {
      avatar: '/src/assets/image/home/<USER>/1.webp',
      name: '<PERSON><PERSON>',
      tag: 'Brand',
    },
    title: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod',
    content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
    image: '/src/assets/image/home/<USER>/1.webp',
    stats: {
      likes: '30.1k',
      comments: '1.1k',
      views: '256',
    },
    date: '2025/7/4',
    currentIndex: 1,
    totalImages: 5,
  },
  thumbnailPosts: [
    {
      id: '1',
      image: '/src/assets/image/home/<USER>/1.webp',
      title: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit...',
      user: { name: 'Milla' },
      stats: { likes: '30.1k' },
      selected: true,
    },
    {
      id: '2',
      image: '/src/assets/image/home/<USER>/2.webp',
      title: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit...',
      user: { name: 'Milla' },
      stats: { likes: '30.1k' },
    },
    {
      id: '3',
      image: '/src/assets/image/home/<USER>/3.webp',
      title: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit...',
      user: { name: 'Milla' },
      stats: { likes: '30.1k' },
    },
    {
      id: '4',
      image: '/src/assets/image/home/<USER>/4.webp',
      title: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit...',
      user: { name: 'Milla' },
      stats: { likes: '30.1k' },
    },
    {
      id: '5',
      image: '/src/assets/image/home/<USER>/5.webp',
      title: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit...',
      user: { name: 'Milla' },
      stats: { likes: '30.1k' },
    },
  ],
}

function RednoteReferenceList() {
  const [selectedCategory, setSelectedCategory] = useState('Brand')
  const [selectedReference, setSelectedReference] = useState(false)

  return (
    <div className="mx-auto max-w-4xl rounded-xl bg-white p-6 shadow-lg">
      {/* Header */}
      <div className="mb-6">
        <h2 className="mb-2 text-2xl text-gray-900 font-semibold">Rednote Reference List</h2>
        <p className="text-sm text-gray-600 leading-relaxed">
          Choose your template Select a reference from your Rednote list to use as your starting point.
        </p>
      </div>

      {/* Category Filter */}
      <div className="mb-6 flex items-center border-b border-gray-200 pb-4">
        <span className="mr-4 text-sm text-gray-700 font-medium">Post Categories:</span>
        <Select
          value={ selectedCategory }
          onChange={ setSelectedCategory }
          className="min-w-[120px] [&_.ant-select-selector]:!border-none [&_.ant-select-selector]:!bg-transparent [&_.ant-select-selection-item]:!text-gray-700 [&_.ant-select-selection-item]:!font-medium [&_.ant-select-selector]:!shadow-none"
          suffixIcon={ <div className="rotate-180 text-xs text-gray-400">^</div> }
        >
          <Select.Option value="Brand">Brand</Select.Option>
          <Select.Option value="Lifestyle">Lifestyle</Select.Option>
          <Select.Option value="Fashion">Fashion</Select.Option>
        </Select>
      </div>

      {/* Main Content Card */}
      <div className="mb-4 flex overflow-hidden border-2 border-blue-100 rounded-xl bg-gray-50">
        {/* Image Section */}
        <div className="relative flex-1">
          <div className="relative h-96 w-full">
            <img
              src={ mockData.mainPost.image }
              alt="Main post"
              className="h-full w-full object-cover"
            />
            <div className="absolute right-4 top-4 rounded-xl bg-black bg-opacity-60 px-2 py-1 text-xs text-white">
              {mockData.mainPost.currentIndex}
              /
              {mockData.mainPost.totalImages}
            </div>
            <button className="absolute left-3 top-1/2 h-8 w-8 flex items-center justify-center rounded-full bg-white bg-opacity-90 text-lg text-gray-700 transition-colors -translate-y-1/2 hover:bg-white">
              ‹
            </button>
            <button className="absolute right-3 top-1/2 h-8 w-8 flex items-center justify-center rounded-full bg-white bg-opacity-90 text-lg text-gray-700 transition-colors -translate-y-1/2 hover:bg-white">
              ›
            </button>
          </div>
        </div>

        {/* Content Section */}
        <div className="flex flex-1 flex-col p-6">
          {/* User Info */}
          <div className="mb-4 flex items-center">
            <img
              src={ mockData.mainPost.user.avatar }
              alt="User avatar"
              className="mr-2 h-8 w-8 rounded-full"
            />
            <span className="mr-2 text-gray-700 font-medium">{mockData.mainPost.user.name}</span>
            <span className="rounded-xl bg-blue-50 px-2 py-1 text-xs text-blue-600">
              {mockData.mainPost.user.tag}
            </span>
          </div>

          {/* Post Content */}
          <div className="mb-4 flex-1">
            <h3 className="mb-3 text-base text-gray-700 font-semibold leading-tight">
              {mockData.mainPost.title}
            </h3>
            <p className="text-sm text-gray-600 leading-relaxed">
              {mockData.mainPost.content}
            </p>
          </div>

          {/* Stats and Actions */}
          <div className="mb-4 flex items-center justify-between border-t border-gray-200 pt-4">
            <div className="flex items-center gap-4">
              <span className="flex items-center gap-1 text-sm text-gray-600">
                <Heart size={ 16 } />
                {mockData.mainPost.stats.likes}
              </span>
              <span className="flex items-center gap-1 text-sm text-gray-600">
                <MessageCircle size={ 16 } />
                {mockData.mainPost.stats.comments}
              </span>
              <span className="flex items-center gap-1 text-sm text-gray-600">
                <Eye size={ 16 } />
                {mockData.mainPost.stats.views}
              </span>
              <span className="text-sm text-gray-400">{mockData.mainPost.date}</span>
            </div>
            <Button
              type="link"
              className="flex items-center gap-1 text-sm !p-0 !text-blue-600"
            >
              View Post
              <Share size={ 16 } />
            </Button>
          </div>

          {/* Select Reference Checkbox */}
          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              id="selectReference"
              checked={ selectedReference }
              onChange={ e => setSelectedReference(e.target.checked) }
              className="h-4 w-4 accent-blue-600"
            />
            <label htmlFor="selectReference" className="cursor-pointer text-sm text-gray-700">
              Select as my reference
            </label>
          </div>
        </div>
      </div>

      {/* Warning Message */}
      <div className="mb-6 flex items-start gap-3 border border-yellow-200 rounded-lg bg-yellow-50 p-4">
        <div className="text-lg text-yellow-600 font-bold">⚠</div>
        <div className="flex-1">
          <div className="mb-1 text-gray-700 font-semibold">You can only choose one</div>
          <div className="text-sm text-gray-600 leading-relaxed">
            Choose either one trending topic or a reference post. We will create your posts based on your choice
          </div>
        </div>
      </div>

      {/* Thumbnail Carousel */}
      <div className="relative flex items-center gap-3">
        <button className="h-8 w-8 flex flex-shrink-0 items-center justify-center border border-gray-300 rounded-full bg-gray-100 text-base text-gray-600 transition-colors hover:bg-gray-200">
          ‹
        </button>
        <div className="scrollbar-hide flex flex-1 gap-4 overflow-x-auto py-2">
          {mockData.thumbnailPosts.map(post => (
            <div
              key={ post.id }
              className={ classNames(
                'relative min-w-[200px] cursor-pointer overflow-hidden rounded-lg bg-white shadow-md transition-transform hover:-translate-y-1',
                {
                  'border-2 border-blue-600': post.selected,
                },
              ) }
            >
              {post.selected && (
                <div className="absolute left-2 top-2 z-10 rounded-xl bg-blue-600 px-2 py-1 text-xs text-white">
                  Selected
                </div>
              )}
              <img
                src={ post.image }
                alt="Thumbnail"
                className="h-30 w-full object-cover"
              />
              <div className="p-3">
                <p className="line-clamp-2 mb-2 text-xs text-gray-700 leading-tight">
                  {post.title}
                </p>
                <div className="flex items-center gap-1.5">
                  <img
                    src="/src/assets/image/home/<USER>/1.webp"
                    alt="User"
                    className="h-4 w-4 rounded-full"
                  />
                  <span className="flex-1 text-xs text-gray-600">{post.user.name}</span>
                  <span className="flex items-center gap-1 text-xs text-gray-400">
                    <Heart size={ 12 } />
                    {post.stats.likes}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
        <button className="h-8 w-8 flex flex-shrink-0 items-center justify-center border border-gray-300 rounded-full bg-gray-100 text-base text-gray-600 transition-colors hover:bg-gray-200">
          ›
        </button>
      </div>
    </div>
  )
}

export default RednoteReferenceList
